# Claudia 桌面版使用指南

## 🎉 构建成功

Claudia 桌面版已成功构建！exe文件位置：
```
src-tauri/target/release/claudia.exe
```

## 🚀 启动应用

### 方法1：使用启动脚本（推荐）
双击运行：`启动claudia.bat`

### 方法2：直接运行
双击：`src-tauri/target/release/claudia.exe`

## ❗ 如果遇到启动问题

### 错误：无法定位序数输入点 TaskDialogIndirect

这是Windows运行时库兼容性问题，解决方案：

#### 1. 安装 Visual C++ Redistributable（推荐）
下载并安装：https://aka.ms/vs/17/release/vc_redist.x64.exe

#### 2. 检查Windows版本
- 需要 Windows 7 及以上版本
- 推荐 Windows 10/11

#### 3. 以管理员身份运行
- 右键点击 `启动claudia.bat`
- 选择"以管理员身份运行"

## ✅ 功能特性

### 完整中文界面
- 所有界面元素都是中文
- 比原版更完善的中文支持

### 高级功能
- ✅ Slash Commands - 自定义命令系统
- ✅ Hooks Editor - 钩子配置编辑器
- ✅ Project Settings - 项目设置管理
- ✅ Claude API - 支持最新Claude模型

### 版本信息
- 版本：1.0.43（从1.0.41更新）
- 构建：Windows x64 原生应用
- 运行时：静态链接，减少依赖

## 🔧 技术信息

### 构建配置
- 前端：React + TypeScript + Vite
- 后端：Rust + Tauri
- 目标：x86_64-pc-windows-msvc
- 优化：Release构建，性能优化

### 文件大小
约 15-20MB（包含所有依赖）

## 📞 支持

如果遇到其他问题：
1. 检查Windows版本兼容性
2. 确保安装了Visual C++ Redistributable
3. 尝试以管理员身份运行
4. 检查防病毒软件是否阻止运行

## 🎯 下一步

应用已可正常使用，享受更新后的Claudia体验！
