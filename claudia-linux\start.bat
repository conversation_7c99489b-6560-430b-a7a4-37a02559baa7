@echo off
echo Starting Super Intelligence Desktop App...
echo.
if not exist "src-tauri\target\release\super-intelligence.exe" (
    echo Error: Application file not found!
    echo Please build the application first: npm run tauri build
    pause
    exit /b 1
)

echo Launching application...
start "" "src-tauri\target\release\super-intelligence.exe"
echo.
echo Application started successfully!
echo If the app doesn't appear, please check:
echo 1. Antivirus software blocking
echo 2. Windows compatibility
echo 3. Try running as administrator
echo.
pause
