D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\deps\libclaudia_lib.rlib: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\process\mod.rs src\process\registry.rs D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\super-intelligence-ab48b9b8031f9379\out/8165c04e4f44d878326c477b47a99b1349f30bebc24cdd6c375c0498ca9914be

D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\deps\claudia_lib.dll: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\process\mod.rs src\process\registry.rs D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\super-intelligence-ab48b9b8031f9379\out/8165c04e4f44d878326c477b47a99b1349f30bebc24cdd6c375c0498ca9914be

D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\deps\claudia_lib.lib: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\process\mod.rs src\process\registry.rs D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\super-intelligence-ab48b9b8031f9379\out/8165c04e4f44d878326c477b47a99b1349f30bebc24cdd6c375c0498ca9914be

D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\deps\claudia_lib.d: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\process\mod.rs src\process\registry.rs D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\super-intelligence-ab48b9b8031f9379\out/8165c04e4f44d878326c477b47a99b1349f30bebc24cdd6c375c0498ca9914be

src\lib.rs:
src\checkpoint\mod.rs:
src\checkpoint\manager.rs:
src\checkpoint\state.rs:
src\checkpoint\storage.rs:
src\claude_binary.rs:
src\commands\mod.rs:
src\commands\agents.rs:
src\commands\claude.rs:
src\commands\mcp.rs:
src\commands\usage.rs:
src\commands\storage.rs:
src\commands\slash_commands.rs:
src\process\mod.rs:
src\process\registry.rs:
D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\super-intelligence-ab48b9b8031f9379\out/8165c04e4f44d878326c477b47a99b1349f30bebc24cdd6c375c0498ca9914be:

# env-dep:CARGO_MANIFEST_DIR=D:\\ubgongxiang\\11\\claudia\\claudia-linux\\src-tauri
# env-dep:CARGO_PKG_AUTHORS=mufeedvh:123vviekr
# env-dep:CARGO_PKG_DESCRIPTION=超级智体 - GUI app and Toolkit for Claude Code
# env-dep:CARGO_PKG_NAME=super-intelligence
# env-dep:OUT_DIR=D:\\ubgongxiang\\11\\claudia\\claudia-linux\\src-tauri\\target\\debug\\build\\super-intelligence-ab48b9b8031f9379\\out
