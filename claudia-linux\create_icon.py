#!/usr/bin/env python3
"""
创建Claudia应用图标
生成一个简单但有效的ICO格式图标文件
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_claudia_icon():
    """创建Claudia应用图标"""
    
    # 创建不同尺寸的图标
    sizes = [16, 32, 48, 64, 128, 256]
    images = []
    
    for size in sizes:
        # 创建图像
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 背景渐变色 (深蓝到紫色，类似Claude的品牌色)
        for y in range(size):
            ratio = y / size
            r = int(45 + (120 - 45) * ratio)  # 45 -> 120
            g = int(55 + (80 - 55) * ratio)   # 55 -> 80  
            b = int(120 + (200 - 120) * ratio) # 120 -> 200
            draw.line([(0, y), (size, y)], fill=(r, g, b, 255))
        
        # 添加圆形边框
        border_width = max(1, size // 32)
        draw.ellipse([border_width, border_width, size-border_width, size-border_width], 
                    outline=(255, 255, 255, 200), width=border_width)
        
        # 添加字母 "C" (代表Claudia)
        if size >= 32:
            try:
                # 尝试使用系统字体
                font_size = size // 3
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                # 如果没有找到字体，使用默认字体
                font = ImageFont.load_default()
            
            # 计算文字位置
            text = "C"
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (size - text_width) // 2
            y = (size - text_height) // 2 - 2
            
            # 绘制文字阴影
            draw.text((x+1, y+1), text, font=font, fill=(0, 0, 0, 100))
            # 绘制文字
            draw.text((x, y), text, font=font, fill=(255, 255, 255, 255))
        
        images.append(img)
    
    return images

def save_icon_files(images):
    """保存图标文件"""
    icons_dir = "src-tauri/icons"
    
    # 确保图标目录存在
    os.makedirs(icons_dir, exist_ok=True)
    
    # 保存PNG格式图标
    sizes = [16, 32, 48, 64, 128, 256]
    for i, size in enumerate(sizes):
        if size == 32:
            images[i].save(f"{icons_dir}/32x32.png", "PNG")
        elif size == 128:
            images[i].save(f"{icons_dir}/128x128.png", "PNG")
            images[i].save(f"{icons_dir}/<EMAIL>", "PNG")
        elif size == 256:
            images[i].save(f"{icons_dir}/icon.png", "PNG")
    
    # 保存ICO格式图标 (包含多个尺寸)
    try:
        # 选择适合ICO的尺寸
        ico_images = [images[i] for i, size in enumerate(sizes) if size <= 256]
        ico_images[0].save(f"{icons_dir}/icon.ico", "ICO", 
                          sizes=[(img.size[0], img.size[1]) for img in ico_images],
                          append_images=ico_images[1:])
        print("✅ 成功创建 icon.ico")
    except Exception as e:
        print(f"❌ 创建ICO文件失败: {e}")
        # 如果ICO创建失败，创建一个简单的32x32 ICO
        images[1].save(f"{icons_dir}/icon.ico", "ICO")
        print("✅ 创建了简化版 icon.ico")
    
    print(f"✅ 图标文件已保存到 {icons_dir}/")
    return True

if __name__ == "__main__":
    print("🎨 正在创建Claudia应用图标...")
    
    try:
        # 创建图标
        images = create_claudia_icon()
        
        # 保存图标文件
        save_icon_files(images)
        
        print("🎉 图标创建完成！")
        
    except Exception as e:
        print(f"❌ 图标创建失败: {e}")
        print("请确保已安装 Pillow 库: pip install Pillow")
