{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 12508179371134305739], [13919194856117907555, "build_script_build", false, 4206165935854340174], [3834743577069889284, "build_script_build", false, 2608572842288436400], [13890802266741835355, "build_script_build", false, 10008283572597074438], [246920333930397414, "build_script_build", false, 281183937376684899], [15441187897486245138, "build_script_build", false, 14424987927779795134], [7849236192756901113, "build_script_build", false, 10150876488121630605], [17962022290347926134, "build_script_build", false, 4926404088643113347], [1582828171158827377, "build_script_build", false, 5160990042328742064], [18440762029541581206, "build_script_build", false, 5816821346018289143], [3755480842441338494, "build_script_build", false, 12156207908444138174]], "local": [{"RerunIfChanged": {"output": "release\\build\\超级智体-504ef8a7a551b5b6\\output", "paths": ["tauri.conf.json", "capabilities", "binaries\\claude-code-x86_64-pc-windows-msvc.exe"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}