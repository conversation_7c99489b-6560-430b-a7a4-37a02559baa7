cargo:rerun-if-env-changed=LIBSQLITE3_SYS_USE_PKG_CONFIG
cargo:include=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1/sqlite3
cargo:rerun-if-changed=sqlite3/sqlite3.c
cargo:rerun-if-changed=sqlite3/wasm32-wasi-vfs.c
cargo:rerun-if-env-changed=SQLITE_MAX_VARIABLE_NUMBER
cargo:rerun-if-env-changed=SQLITE_MAX_EXPR_DEPTH
cargo:rerun-if-env-changed=SQLITE_MAX_COLUMN
cargo:rerun-if-env-changed=LIBSQLITE3_FLAGS
OUT_DIR = Some(D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\libsqlite3-sys-2b84a2aaff4871cd\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\deps;D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;D:\ubgongxiang\11\claudia\claudia-linux\node_modules\.bin;D:\ubgongxiang\11\claudia\node_modules\.bin;D:\ubgongxiang\11\node_modules\.bin;D:\ubgongxiang\node_modules\.bin;D:\node_modules\.bin;C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;C:\Program Files\Python311\Scripts\;C:\Program Files\Python311\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\.local\bin;c:\Users\<USER>\.cursor\extensions\ms-python.python-2025.6.1-win32-x64\python_files\deactivate\powershell;c:\Users\<USER>\Desktop\逆向\.venv\Scripts;c:\Users\<USER>\.cursor\extensions\ms-python.python-2025.6.1-win32-x64\python_files\deactivate\powershell;c:\Users\<USER>\Desktop\逆向\.venv\Scripts;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\libnvvp;C:\Program Files\Python311\Scripts\;C:\Program Files\Python311\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.0\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Lingma\bin;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\.local\bin;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\scripts\noConfigScripts)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
sqlite3.c
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64
cargo:rustc-link-lib=static=sqlite3
cargo:rustc-link-search=native=D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\libsqlite3-sys-2b84a2aaff4871cd\out
cargo:lib_dir=D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\libsqlite3-sys-2b84a2aaff4871cd\out
