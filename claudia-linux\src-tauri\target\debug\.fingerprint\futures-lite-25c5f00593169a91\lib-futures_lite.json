{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"fastrand\", \"futures-io\", \"parking\", \"race\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"fastrand\", \"futures-io\", \"memchr\", \"parking\", \"race\", \"std\"]", "target": 4894038637245960899, "profile": 15657897354478470176, "path": 7296979139388819200, "deps": [[5103565458935487, "futures_io", false, 15584616425289904539], [189982446159473706, "parking", false, 13085198831281002931], [1906322745568073236, "pin_project_lite", false, 12513370387149380344], [7620660491849607393, "futures_core", false, 16792782532608798886], [12285238697122577036, "fastrand", false, 8604706070488127925]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-lite-25c5f00593169a91\\dep-lib-futures_lite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}