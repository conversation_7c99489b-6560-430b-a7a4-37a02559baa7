{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 15657897354478470176, "path": 2331744665283686352, "deps": [[1462335029370885857, "quick_xml", false, 5772420638581700705], [3334271191048661305, "windows_version", false, 9108582887893300040], [10806645703491011684, "thiserror", false, 5650462710600407025], [13116089016666501665, "windows", false, 3466355342227673606]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-ae50cc31c94b4992\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}