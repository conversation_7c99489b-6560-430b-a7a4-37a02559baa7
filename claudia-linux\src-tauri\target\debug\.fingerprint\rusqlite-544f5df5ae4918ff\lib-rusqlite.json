{"rustc": 16591470773350601817, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"preupdate_hook\", \"release_memory\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"with-asan\"]", "target": 10662205063260755052, "profile": 15657897354478470176, "path": 4671569629175566801, "deps": [[3056352129074654578, "hashlink", false, 7949941767340277306], [3666196340704888985, "smallvec", false, 3162946223915527809], [5510864063823219921, "fallible_streaming_iterator", false, 10144246774190536991], [7896293946984509699, "bitflags", false, 15445298471420156998], [12860549049674006569, "fallible_iterator", false, 7289233312157819952], [16675652872862304210, "libsqlite3_sys", false, 17529755588605416536]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rusqlite-544f5df5ae4918ff\\dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}