cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\tauri-plugin-updater-7d816d8dfb028615\out\tauri-plugin-updater-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-updater-2.8.1\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
