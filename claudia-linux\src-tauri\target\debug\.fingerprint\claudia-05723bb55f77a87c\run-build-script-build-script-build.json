{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9805105373369294601, "build_script_build", false, 3072954141978645325], [10755362358622467486, "build_script_build", false, 6471864820242605517], [13919194856117907555, "build_script_build", false, 8026258986896988395], [3834743577069889284, "build_script_build", false, 11331055535303273144], [13890802266741835355, "build_script_build", false, 11422082606362791012], [246920333930397414, "build_script_build", false, 1288983976813487648], [15441187897486245138, "build_script_build", false, 15642777724484006804], [7849236192756901113, "build_script_build", false, 6658266499184673435], [17962022290347926134, "build_script_build", false, 1804827475405308316], [1582828171158827377, "build_script_build", false, 8483448232152489050], [18440762029541581206, "build_script_build", false, 6922021864782957236]], "local": [{"RerunIfChanged": {"output": "debug\\build\\claudia-05723bb55f77a87c\\output", "paths": ["tauri.conf.json", "capabilities", "binaries\\claude-code-x86_64-pc-windows-msvc.exe"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}