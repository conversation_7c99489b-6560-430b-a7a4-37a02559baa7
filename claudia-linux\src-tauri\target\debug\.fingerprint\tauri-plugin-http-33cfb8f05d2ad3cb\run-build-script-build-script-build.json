{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 6471864820242605517], [13890802266741835355, "build_script_build", false, 11422082606362791012], [15441187897486245138, "build_script_build", false, 3080436780985802788]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-http-33cfb8f05d2ad3cb\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}