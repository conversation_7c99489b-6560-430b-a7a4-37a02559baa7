{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 15695838419062756478, "deps": [[5103565458935487, "futures_io", false, 15584616425289904539], [1811549171721445101, "futures_channel", false, 5733939900741872867], [7013762810557009322, "futures_sink", false, 2778825916047483651], [7620660491849607393, "futures_core", false, 16792782532608798886], [10629569228670356391, "futures_util", false, 8612617707734785946], [12779779637805422465, "futures_executor", false, 7720431182306725079], [16240732885093539806, "futures_task", false, 4379749133392367206]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-d6cd48d61843e48f\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}