{"rustc": 16591470773350601817, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"default\", \"min_sqlite_version_3_14_0\", \"pkg-config\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"loadable_extension\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"prettyplease\", \"preupdate_hook\", \"quote\", \"session\", \"sqlcipher\", \"syn\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"with-asan\"]", "target": 14162657976132989036, "profile": 2040997289075261528, "path": 5540797284261329473, "deps": [[16675652872862304210, "build_script_build", false, 14385982341785098342]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\libsqlite3-sys-8f9ed1303ed77f39\\dep-lib-libsqlite3_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}