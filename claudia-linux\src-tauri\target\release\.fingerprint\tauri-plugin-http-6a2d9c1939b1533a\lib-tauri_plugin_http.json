{"rustc": 16591470773350601817, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 2040997289075261528, "path": 2670980187392678219, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 10618455366097760799], [3150220818285335163, "url", false, 14171641885309381843], [8218178811151724123, "reqwest", false, 17973600170047651263], [8298091525883606470, "cookie_store", false, 17503898024052035362], [9010263965687315507, "http", false, 12213520909095809930], [9451456094439810778, "regex", false, 7725510663390383755], [9538054652646069845, "tokio", false, 9712805951141166731], [9689903380558560274, "serde", false, 7541355907771353291], [10755362358622467486, "tauri", false, 263917925848210510], [10806645703491011684, "thiserror", false, 5059168433112344807], [13890802266741835355, "tauri_plugin_fs", false, 17342599419055265076], [15367738274754116744, "serde_json", false, 17470562606684216992], [15441187897486245138, "build_script_build", false, 14424987927779795134], [16066129441945555748, "bytes", false, 18342550410450640939], [17047088963840213854, "data_url", false, 13743673702683544335]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-http-6a2d9c1939b1533a\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}