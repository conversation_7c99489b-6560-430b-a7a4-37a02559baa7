D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\libsqlite3-sys-b143bdbca4f53cfe\build_script_build-b143bdbca4f53cfe.exe: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\build.rs

D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\libsqlite3-sys-b143bdbca4f53cfe\build_script_build-b143bdbca4f53cfe.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\build.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\build.rs:

# env-dep:CARGO_MANIFEST_DIR=C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libsqlite3-sys-0.30.1
