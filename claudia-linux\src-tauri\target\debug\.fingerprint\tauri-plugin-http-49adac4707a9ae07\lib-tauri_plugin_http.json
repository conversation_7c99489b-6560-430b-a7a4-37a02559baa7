{"rustc": 16591470773350601817, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 15657897354478470176, "path": 2670980187392678219, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 12785749307423811727], [3150220818285335163, "url", false, 7821896678146667634], [8218178811151724123, "reqwest", false, 18247497758344678462], [8298091525883606470, "cookie_store", false, 2453417190739602998], [9010263965687315507, "http", false, 18131092521246687413], [9451456094439810778, "regex", false, 5755855846040275993], [9538054652646069845, "tokio", false, 14483482792075840282], [9689903380558560274, "serde", false, 11564652921496065999], [10755362358622467486, "tauri", false, 16124978675318053989], [10806645703491011684, "thiserror", false, 5650462710600407025], [13890802266741835355, "tauri_plugin_fs", false, 14531601899280784996], [15367738274754116744, "serde_json", false, 6550851001671471717], [15441187897486245138, "build_script_build", false, 15642777724484006804], [16066129441945555748, "bytes", false, 4443973527350322883], [17047088963840213854, "data_url", false, 1080168423427546639]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-49adac4707a9ae07\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}