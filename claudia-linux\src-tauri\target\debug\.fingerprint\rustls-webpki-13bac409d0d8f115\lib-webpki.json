{"rustc": 16591470773350601817, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 15657897354478470176, "path": 7438661357301910485, "deps": [[2883436298747778685, "pki_types", false, 1093944903609859800], [5491919304041016563, "ring", false, 14842450034411559943], [8995469080876806959, "untrusted", false, 4291002337649026650]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-webpki-13bac409d0d8f115\\dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}