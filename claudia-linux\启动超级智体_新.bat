@echo off
chcp 65001 >nul
echo ========================================
echo           超级智体 桌面版启动器
echo ========================================
echo.

REM 检查exe文件是否存在
if not exist "src-tauri\target\release\超级智体.exe" (
    echo 错误: 超级智体.exe 文件不存在
    echo 请先运行构建命令: npm run tauri build
    pause
    exit /b 1
)

echo 正在启动 超级智体...
echo.

REM 设置环境变量以提高兼容性
set RUST_BACKTRACE=1

REM 尝试启动应用
echo 启动命令: src-tauri\target\release\超级智体.exe
echo 文件大小: 约25MB
start "" "src-tauri\target\release\超级智体.exe"

REM 等待一下检查是否启动成功
timeout /t 5 /nobreak >nul

echo.
echo 启动命令已执行
echo.
echo 如果应用没有正常显示，请检查：
echo.
echo 1. 是否出现错误对话框？
echo    - 如果提示 "TaskDialogIndirect" 错误
echo    - 请安装: https://aka.ms/vs/17/release/vc_redist.x64.exe
echo.
echo 2. 防病毒软件是否阻止了应用？
echo    - 请将 超级智体.exe 添加到白名单
echo.
echo 3. Windows版本是否兼容？
echo    - 需要 Windows 7 及以上版本
echo.
echo 4. 尝试以管理员身份运行此脚本
echo.
echo 按任意键关闭此窗口...
pause >nul
