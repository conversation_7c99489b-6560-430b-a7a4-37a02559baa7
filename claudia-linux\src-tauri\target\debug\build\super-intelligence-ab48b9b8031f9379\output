cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=so
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=claudia_asterisk
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\super-intelligence-ab48b9b8031f9379\out\app-manifest\__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-pc-windows-msvc
cargo:rerun-if-changed=binaries\claude-code-x86_64-pc-windows-msvc.exe
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=D:\ubgongxiang\11\claudia\claudia-linux\src-tauri\target\debug\build\super-intelligence-ab48b9b8031f9379\out\resource.lib
