{"rustc": 16591470773350601817, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 12121979343078292920, "deps": [[3060637413840920116, "proc_macro2", false, 1922001909018428275], [7341521034400937459, "tauri_codegen", false, 1730189580290860263], [11050281405049894993, "tauri_utils", false, 2603994447889542098], [13077543566650298139, "heck", false, 13957943336384230101], [17990358020177143287, "quote", false, 18281785799565566443], [18149961000318489080, "syn", false, 6319201772271177320]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-d6767da841143f53\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}