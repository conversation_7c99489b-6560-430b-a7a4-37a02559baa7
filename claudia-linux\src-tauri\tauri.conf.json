﻿{
  "$schema": "https://schema.tauri.app/config/2",
  "productName": "<PERSON>",
  "version": "0.1.0",
  "identifier": "claudia.asterisk.so",
  "build": {
    "beforeDevCommand": "npm run dev",
    "devUrl": "http://localhost:1420",
    "beforeBuildCommand": "npm run build",
    "frontendDist": "../dist"
  },
  "app": {
    "windows": [
      {
        "title": "<PERSON>",
        "width": 800,
        "height": 600
      }
    ]
  }
}
