{"rustc": 16591470773350601817, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 897028823148492248, "deps": [[40386456601120721, "percent_encoding", false, 9532286527671409682], [418947936956741439, "h2", false, 11944095620710919324], [778154619793643451, "hyper_util", false, 5718778137013015164], [784494742817713399, "tower_service", false, 9825182280476596558], [1288403060204016458, "tokio_util", false, 222768735023883673], [1788832197870803419, "hyper_rustls", false, 16077093622702872080], [1906322745568073236, "pin_project_lite", false, 12513370387149380344], [2054153378684941554, "tower_http", false, 11258742392063672561], [2517136641825875337, "sync_wrapper", false, 14154279601017349630], [2883436298747778685, "rustls_pki_types", false, 1093944903609859800], [3150220818285335163, "url", false, 7821896678146667634], [5695049318159433696, "tower", false, 569291546811942496], [5907992341687085091, "webpki_roots", false, 12130717419585748600], [5986029879202738730, "log", false, 785926723577402214], [7620660491849607393, "futures_core", false, 16792782532608798886], [8298091525883606470, "cookie_store", false, 2453417190739602998], [9010263965687315507, "http", false, 18131092521246687413], [9538054652646069845, "tokio", false, 14483482792075840282], [9689903380558560274, "serde", false, 11564652921496065999], [10229185211513642314, "mime", false, 11402712181982263359], [10629569228670356391, "futures_util", false, 8612617707734785946], [11895591994124935963, "tokio_rustls", false, 5145093823749184836], [11957360342995674422, "hyper", false, 14851675900839142679], [12186126227181294540, "tokio_native_tls", false, 1123135589072592024], [13077212702700853852, "base64", false, 17439502704752187538], [14084095096285906100, "http_body", false, 13023911694743650655], [14564311161534545801, "encoding_rs", false, 12911914667759783197], [15367738274754116744, "serde_json", false, 6550851001671471717], [16066129441945555748, "bytes", false, 4443973527350322883], [16400140949089969347, "rustls", false, 1990977607492622149], [16542808166767769916, "serde_urlencoded", false, 8970631135165836289], [16727543399706004146, "cookie_crate", false, 17411284503189594469], [16785601910559813697, "native_tls_crate", false, 7099989323727335142], [16900715236047033623, "http_body_util", false, 4508138720400931357], [18273243456331255970, "hyper_tls", false, 5549645485468656394]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-96b6409aa9f0bfc1\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}