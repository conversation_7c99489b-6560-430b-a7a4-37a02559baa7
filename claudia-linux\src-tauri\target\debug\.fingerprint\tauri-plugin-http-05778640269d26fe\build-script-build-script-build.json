{"rustc": 16591470773350601817, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 3227959295260582655, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 10065075675239071166], [2326493920556799156, "tauri_plugin", false, 13179848302418861552], [3150220818285335163, "url", false, 17434621691386602894], [6913375703034175521, "schemars", false, 14835325097186415013], [9451456094439810778, "regex", false, 5755855846040275993], [9689903380558560274, "serde", false, 7378898207542884439]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-05778640269d26fe\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}