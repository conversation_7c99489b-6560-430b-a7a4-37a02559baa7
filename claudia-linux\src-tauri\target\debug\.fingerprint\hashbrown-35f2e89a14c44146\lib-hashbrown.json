{"rustc": 16591470773350601817, "features": "[\"ahash\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 15657897354478470176, "path": 16049748422240844555, "deps": [[966925859616469517, "ahash", false, 6819944788916943628]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-35f2e89a14c44146\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}