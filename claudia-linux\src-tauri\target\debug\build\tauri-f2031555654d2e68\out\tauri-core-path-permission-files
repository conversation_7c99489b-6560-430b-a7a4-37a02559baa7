["\\\\?\\D:\\ubgongxiang\\11\\claudia\\claudia-linux\\src-tauri\\target\\debug\\build\\tauri-f2031555654d2e68\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\ubgongxiang\\11\\claudia\\claudia-linux\\src-tauri\\target\\debug\\build\\tauri-f2031555654d2e68\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\ubgongxiang\\11\\claudia\\claudia-linux\\src-tauri\\target\\debug\\build\\tauri-f2031555654d2e68\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\ubgongxiang\\11\\claudia\\claudia-linux\\src-tauri\\target\\debug\\build\\tauri-f2031555654d2e68\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\ubgongxiang\\11\\claudia\\claudia-linux\\src-tauri\\target\\debug\\build\\tauri-f2031555654d2e68\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\ubgongxiang\\11\\claudia\\claudia-linux\\src-tauri\\target\\debug\\build\\tauri-f2031555654d2e68\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\ubgongxiang\\11\\claudia\\claudia-linux\\src-tauri\\target\\debug\\build\\tauri-f2031555654d2e68\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\ubgongxiang\\11\\claudia\\claudia-linux\\src-tauri\\target\\debug\\build\\tauri-f2031555654d2e68\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\ubgongxiang\\11\\claudia\\claudia-linux\\src-tauri\\target\\debug\\build\\tauri-f2031555654d2e68\\out\\permissions\\path\\autogenerated\\default.toml"]