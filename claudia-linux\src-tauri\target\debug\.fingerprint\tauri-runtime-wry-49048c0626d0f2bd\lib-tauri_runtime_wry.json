{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 15354354413164387873, "deps": [[376837177317575824, "softbuffer", false, 13522179247975162068], [442785307232013896, "tauri_runtime", false, 10282853642438293076], [3150220818285335163, "url", false, 7821896678146667634], [3722963349756955755, "once_cell", false, 9369741635030667748], [4143744114649553716, "raw_window_handle", false, 4590846174750262984], [5986029879202738730, "log", false, 785926723577402214], [7752760652095876438, "build_script_build", false, 10922701282505622924], [8539587424388551196, "webview2_com", false, 15446265367576468344], [9010263965687315507, "http", false, 18131092521246687413], [11050281405049894993, "tauri_utils", false, 12838874062673002299], [13116089016666501665, "windows", false, 3466355342227673606], [13223659721939363523, "tao", false, 7943257932653385463], [14794439852947137341, "wry", false, 11702999506690950044]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-49048c0626d0f2bd\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}