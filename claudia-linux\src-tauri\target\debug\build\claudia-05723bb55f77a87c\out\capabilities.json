{"default": {"identifier": "default", "description": "Capability for the main window", "local": true, "windows": ["main"], "permissions": ["core:default", "dialog:default", "dialog:allow-open", "dialog:allow-save", "shell:allow-execute", "shell:allow-spawn", "shell:allow-open", {"identifier": "shell:allow-execute", "allow": [{"args": true, "name": "claude-code", "sidecar": true}, {"args": true, "name": "claude", "sidecar": false}]}, {"identifier": "shell:allow-spawn", "allow": [{"args": true, "name": "claude-code", "sidecar": true}, {"args": true, "name": "claude", "sidecar": false}]}, "fs:default", "fs:allow-mkdir", "fs:allow-read", "fs:allow-write", "fs:allow-remove", "fs:allow-rename", "fs:allow-exists", "fs:allow-copy-file", "fs:read-all", "fs:write-all", "fs:scope-app-recursive", "fs:scope-home-recursive", "http:default", "http:allow-fetch", "process:default", "notification:default", "clipboard-manager:default", "global-shortcut:default", "updater:default"]}}