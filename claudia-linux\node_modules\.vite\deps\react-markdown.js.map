{"version": 3, "sources": ["../../react-markdown/lib/index.js"], "sourcesContent": ["/**\n * @import {<PERSON><PERSON>, ElementContent, No<PERSON>, Parents, Root} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {ComponentProps, ElementType, ReactElement} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList, Processor} from 'unified'\n */\n\n/**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */\n\n/**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */\n\n/**\n * @typedef {{\n *   [Key in Extract<ElementType, string>]?: ElementType<ComponentProps<Key> & ExtraProps>\n * }} Components\n *   Map tag names to components.\n */\n\n/**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {string | null | undefined} [className]\n *   Wrap in a `div` with this class name.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */\n\n/**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */\n\nimport {unreachable} from 'devlop'\nimport {toJsxRuntime} from 'hast-util-to-jsx-runtime'\nimport {urlAttributes} from 'html-url-attributes'\nimport {Fragment, jsx, jsxs} from 'react/jsx-runtime'\nimport {createElement, useEffect, useState} from 'react'\nimport remarkParse from 'remark-parse'\nimport remarkRehype from 'remark-rehype'\nimport {unified} from 'unified'\nimport {visit} from 'unist-util-visit'\nimport {VFile} from 'vfile'\n\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/** @type {PluggableList} */\nconst emptyPlugins = []\n/** @type {Readonly<RemarkRehypeOptions>} */\nconst emptyRemarkRehypeOptions = {allowDangerousHtml: true}\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i\n\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */\nconst deprecations = [\n  {from: 'astPlugins', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'allowDangerousHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {\n    from: 'allowNode',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowElement'\n  },\n  {\n    from: 'allowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowedElements'\n  },\n  {\n    from: 'disallowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'disallowedElements'\n  },\n  {from: 'escapeHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'includeElementIndex', id: '#remove-includeelementindex'},\n  {\n    from: 'includeNodeIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  },\n  {from: 'linkTarget', id: 'remove-linktarget'},\n  {from: 'plugins', id: 'change-plugins-to-remarkplugins', to: 'remarkPlugins'},\n  {from: 'rawSourcePos', id: '#remove-rawsourcepos'},\n  {from: 'renderers', id: 'change-renderers-to-components', to: 'components'},\n  {from: 'source', id: 'change-source-to-children', to: 'children'},\n  {from: 'sourcePos', id: '#remove-sourcepos'},\n  {from: 'transformImageUri', id: '#add-urltransform', to: 'urlTransform'},\n  {from: 'transformLinkUri', id: '#add-urltransform', to: 'urlTransform'}\n]\n\n/**\n * Component to render markdown.\n *\n * This is a synchronous component.\n * When using async plugins,\n * see {@linkcode MarkdownAsync} or {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nexport function Markdown(options) {\n  const processor = createProcessor(options)\n  const file = createFile(options)\n  return post(processor.runSync(processor.parse(file), file), options)\n}\n\n/**\n * Component to render markdown with support for async plugins\n * through async/await.\n *\n * Components returning promises are supported on the server.\n * For async support on the client,\n * see {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Promise<ReactElement>}\n *   Promise to a React element.\n */\nexport async function MarkdownAsync(options) {\n  const processor = createProcessor(options)\n  const file = createFile(options)\n  const tree = await processor.run(processor.parse(file), file)\n  return post(tree, options)\n}\n\n/**\n * Component to render markdown with support for async plugins through hooks.\n *\n * This uses `useEffect` and `useState` hooks.\n * Hooks run on the client and do not immediately render something.\n * For async support on the server,\n * see {@linkcode MarkdownAsync}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nexport function MarkdownHooks(options) {\n  const processor = createProcessor(options)\n  const [error, setError] = useState(\n    /** @type {Error | undefined} */ (undefined)\n  )\n  const [tree, setTree] = useState(/** @type {Root | undefined} */ (undefined))\n\n  useEffect(\n    /* c8 ignore next 7 -- hooks are client-only. */\n    function () {\n      const file = createFile(options)\n      processor.run(processor.parse(file), file, function (error, tree) {\n        setError(error)\n        setTree(tree)\n      })\n    },\n    [\n      options.children,\n      options.rehypePlugins,\n      options.remarkPlugins,\n      options.remarkRehypeOptions\n    ]\n  )\n\n  /* c8 ignore next -- hooks are client-only. */\n  if (error) throw error\n\n  /* c8 ignore next -- hooks are client-only. */\n  return tree ? post(tree, options) : createElement(Fragment)\n}\n\n/**\n * Set up the `unified` processor.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Processor<MdastRoot, MdastRoot, Root, undefined, undefined>}\n *   Result.\n */\nfunction createProcessor(options) {\n  const rehypePlugins = options.rehypePlugins || emptyPlugins\n  const remarkPlugins = options.remarkPlugins || emptyPlugins\n  const remarkRehypeOptions = options.remarkRehypeOptions\n    ? {...options.remarkRehypeOptions, ...emptyRemarkRehypeOptions}\n    : emptyRemarkRehypeOptions\n\n  const processor = unified()\n    .use(remarkParse)\n    .use(remarkPlugins)\n    .use(remarkRehype, remarkRehypeOptions)\n    .use(rehypePlugins)\n\n  return processor\n}\n\n/**\n * Set up the virtual file.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {VFile}\n *   Result.\n */\nfunction createFile(options) {\n  const children = options.children || ''\n  const file = new VFile()\n\n  if (typeof children === 'string') {\n    file.value = children\n  } else {\n    unreachable(\n      'Unexpected value `' +\n        children +\n        '` for `children` prop, expected `string`'\n    )\n  }\n\n  return file\n}\n\n/**\n * Process the result from unified some more.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction post(tree, options) {\n  const allowedElements = options.allowedElements\n  const allowElement = options.allowElement\n  const components = options.components\n  const disallowedElements = options.disallowedElements\n  const skipHtml = options.skipHtml\n  const unwrapDisallowed = options.unwrapDisallowed\n  const urlTransform = options.urlTransform || defaultUrlTransform\n\n  for (const deprecation of deprecations) {\n    if (Object.hasOwn(options, deprecation.from)) {\n      unreachable(\n        'Unexpected `' +\n          deprecation.from +\n          '` prop, ' +\n          (deprecation.to\n            ? 'use `' + deprecation.to + '` instead'\n            : 'remove it') +\n          ' (see <' +\n          changelog +\n          '#' +\n          deprecation.id +\n          '> for more info)'\n      )\n    }\n  }\n\n  if (allowedElements && disallowedElements) {\n    unreachable(\n      'Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other'\n    )\n  }\n\n  // Wrap in `div` if there’s a class name.\n  if (options.className) {\n    tree = {\n      type: 'element',\n      tagName: 'div',\n      properties: {className: options.className},\n      // Assume no doctypes.\n      children: /** @type {Array<ElementContent>} */ (\n        tree.type === 'root' ? tree.children : [tree]\n      )\n    }\n  }\n\n  visit(tree, transform)\n\n  return toJsxRuntime(tree, {\n    Fragment,\n    // @ts-expect-error\n    // React components are allowed to return numbers,\n    // but not according to the types in hast-util-to-jsx-runtime\n    components,\n    ignoreInvalidStyle: true,\n    jsx,\n    jsxs,\n    passKeys: true,\n    passNode: true\n  })\n\n  /** @type {BuildVisitor<Root>} */\n  function transform(node, index, parent) {\n    if (node.type === 'raw' && parent && typeof index === 'number') {\n      if (skipHtml) {\n        parent.children.splice(index, 1)\n      } else {\n        parent.children[index] = {type: 'text', value: node.value}\n      }\n\n      return index\n    }\n\n    if (node.type === 'element') {\n      /** @type {string} */\n      let key\n\n      for (key in urlAttributes) {\n        if (\n          Object.hasOwn(urlAttributes, key) &&\n          Object.hasOwn(node.properties, key)\n        ) {\n          const value = node.properties[key]\n          const test = urlAttributes[key]\n          if (test === null || test.includes(node.tagName)) {\n            node.properties[key] = urlTransform(String(value || ''), key, node)\n          }\n        }\n      }\n    }\n\n    if (node.type === 'element') {\n      let remove = allowedElements\n        ? !allowedElements.includes(node.tagName)\n        : disallowedElements\n          ? disallowedElements.includes(node.tagName)\n          : false\n\n      if (!remove && allowElement && typeof index === 'number') {\n        remove = !allowElement(node, index, parent)\n      }\n\n      if (remove && parent && typeof index === 'number') {\n        if (unwrapDisallowed && node.children) {\n          parent.children.splice(index, 1, ...node.children)\n        } else {\n          parent.children.splice(index, 1)\n        }\n\n        return index\n      }\n    }\n  }\n}\n\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */\nexport function defaultUrlTransform(value) {\n  // Same as:\n  // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n  // But without the `encode` part.\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon === -1 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash !== -1 && colon > slash) ||\n    (questionMark !== -1 && colon > questionMark) ||\n    (numberSign !== -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    safeProtocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAiGA,yBAAkC;AAClC,mBAAiD;AAOjD,IAAM,YACJ;AAGF,IAAM,eAAe,CAAC;AAEtB,IAAM,2BAA2B,EAAC,oBAAoB,KAAI;AAC1D,IAAM,eAAe;AAIrB,IAAM,eAAe;AAAA,EACnB,EAAC,MAAM,cAAc,IAAI,uCAAsC;AAAA,EAC/D,EAAC,MAAM,sBAAsB,IAAI,uCAAsC;AAAA,EACvE;AAAA,IACE,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,EAAC,MAAM,cAAc,IAAI,uCAAsC;AAAA,EAC/D,EAAC,MAAM,uBAAuB,IAAI,8BAA6B;AAAA,EAC/D;AAAA,IACE,MAAM;AAAA,IACN,IAAI;AAAA,EACN;AAAA,EACA,EAAC,MAAM,cAAc,IAAI,oBAAmB;AAAA,EAC5C,EAAC,MAAM,WAAW,IAAI,mCAAmC,IAAI,gBAAe;AAAA,EAC5E,EAAC,MAAM,gBAAgB,IAAI,uBAAsB;AAAA,EACjD,EAAC,MAAM,aAAa,IAAI,kCAAkC,IAAI,aAAY;AAAA,EAC1E,EAAC,MAAM,UAAU,IAAI,6BAA6B,IAAI,WAAU;AAAA,EAChE,EAAC,MAAM,aAAa,IAAI,oBAAmB;AAAA,EAC3C,EAAC,MAAM,qBAAqB,IAAI,qBAAqB,IAAI,eAAc;AAAA,EACvE,EAAC,MAAM,oBAAoB,IAAI,qBAAqB,IAAI,eAAc;AACxE;AAcO,SAAS,SAAS,SAAS;AAChC,QAAM,YAAY,gBAAgB,OAAO;AACzC,QAAM,OAAO,WAAW,OAAO;AAC/B,SAAO,KAAK,UAAU,QAAQ,UAAU,MAAM,IAAI,GAAG,IAAI,GAAG,OAAO;AACrE;AAeA,eAAsB,cAAc,SAAS;AAC3C,QAAM,YAAY,gBAAgB,OAAO;AACzC,QAAM,OAAO,WAAW,OAAO;AAC/B,QAAM,OAAO,MAAM,UAAU,IAAI,UAAU,MAAM,IAAI,GAAG,IAAI;AAC5D,SAAO,KAAK,MAAM,OAAO;AAC3B;AAeO,SAAS,cAAc,SAAS;AACrC,QAAM,YAAY,gBAAgB,OAAO;AACzC,QAAM,CAAC,OAAO,QAAQ,QAAI;AAAA;AAAA,IACU;AAAA,EACpC;AACA,QAAM,CAAC,MAAM,OAAO,QAAI;AAAA;AAAA,IAA0C;AAAA,EAAU;AAE5E;AAAA;AAAA,IAEE,WAAY;AACV,YAAM,OAAO,WAAW,OAAO;AAC/B,gBAAU,IAAI,UAAU,MAAM,IAAI,GAAG,MAAM,SAAUA,QAAOC,OAAM;AAChE,iBAASD,MAAK;AACd,gBAAQC,KAAI;AAAA,MACd,CAAC;AAAA,IACH;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AAGA,MAAI,MAAO,OAAM;AAGjB,SAAO,OAAO,KAAK,MAAM,OAAO,QAAI,4BAAc,2BAAQ;AAC5D;AAUA,SAAS,gBAAgB,SAAS;AAChC,QAAM,gBAAgB,QAAQ,iBAAiB;AAC/C,QAAM,gBAAgB,QAAQ,iBAAiB;AAC/C,QAAM,sBAAsB,QAAQ,sBAChC,EAAC,GAAG,QAAQ,qBAAqB,GAAG,yBAAwB,IAC5D;AAEJ,QAAM,YAAY,QAAQ,EACvB,IAAI,WAAW,EACf,IAAI,aAAa,EACjB,IAAI,cAAc,mBAAmB,EACrC,IAAI,aAAa;AAEpB,SAAO;AACT;AAUA,SAAS,WAAW,SAAS;AAC3B,QAAM,WAAW,QAAQ,YAAY;AACrC,QAAM,OAAO,IAAI,MAAM;AAEvB,MAAI,OAAO,aAAa,UAAU;AAChC,SAAK,QAAQ;AAAA,EACf,OAAO;AACL;AAAA,MACE,uBACE,WACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAAS,KAAK,MAAM,SAAS;AAC3B,QAAM,kBAAkB,QAAQ;AAChC,QAAM,eAAe,QAAQ;AAC7B,QAAM,aAAa,QAAQ;AAC3B,QAAM,qBAAqB,QAAQ;AACnC,QAAM,WAAW,QAAQ;AACzB,QAAM,mBAAmB,QAAQ;AACjC,QAAM,eAAe,QAAQ,gBAAgB;AAE7C,aAAW,eAAe,cAAc;AACtC,QAAI,OAAO,OAAO,SAAS,YAAY,IAAI,GAAG;AAC5C;AAAA,QACE,iBACE,YAAY,OACZ,cACC,YAAY,KACT,UAAU,YAAY,KAAK,cAC3B,eACJ,YACA,YACA,MACA,YAAY,KACZ;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AAEA,MAAI,mBAAmB,oBAAoB;AACzC;AAAA,MACE;AAAA,IACF;AAAA,EACF;AAGA,MAAI,QAAQ,WAAW;AACrB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,EAAC,WAAW,QAAQ,UAAS;AAAA;AAAA,MAEzC;AAAA;AAAA,QACE,KAAK,SAAS,SAAS,KAAK,WAAW,CAAC,IAAI;AAAA;AAAA,IAEhD;AAAA,EACF;AAEA,QAAM,MAAM,SAAS;AAErB,SAAO,aAAa,MAAM;AAAA,IACxB;AAAA;AAAA;AAAA;AAAA,IAIA;AAAA,IACA,oBAAoB;AAAA,IACpB;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAGD,WAAS,UAAU,MAAM,OAAO,QAAQ;AACtC,QAAI,KAAK,SAAS,SAAS,UAAU,OAAO,UAAU,UAAU;AAC9D,UAAI,UAAU;AACZ,eAAO,SAAS,OAAO,OAAO,CAAC;AAAA,MACjC,OAAO;AACL,eAAO,SAAS,KAAK,IAAI,EAAC,MAAM,QAAQ,OAAO,KAAK,MAAK;AAAA,MAC3D;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,SAAS,WAAW;AAE3B,UAAI;AAEJ,WAAK,OAAO,eAAe;AACzB,YACE,OAAO,OAAO,eAAe,GAAG,KAChC,OAAO,OAAO,KAAK,YAAY,GAAG,GAClC;AACA,gBAAM,QAAQ,KAAK,WAAW,GAAG;AACjC,gBAAM,OAAO,cAAc,GAAG;AAC9B,cAAI,SAAS,QAAQ,KAAK,SAAS,KAAK,OAAO,GAAG;AAChD,iBAAK,WAAW,GAAG,IAAI,aAAa,OAAO,SAAS,EAAE,GAAG,KAAK,IAAI;AAAA,UACpE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,SAAS,WAAW;AAC3B,UAAI,SAAS,kBACT,CAAC,gBAAgB,SAAS,KAAK,OAAO,IACtC,qBACE,mBAAmB,SAAS,KAAK,OAAO,IACxC;AAEN,UAAI,CAAC,UAAU,gBAAgB,OAAO,UAAU,UAAU;AACxD,iBAAS,CAAC,aAAa,MAAM,OAAO,MAAM;AAAA,MAC5C;AAEA,UAAI,UAAU,UAAU,OAAO,UAAU,UAAU;AACjD,YAAI,oBAAoB,KAAK,UAAU;AACrC,iBAAO,SAAS,OAAO,OAAO,GAAG,GAAG,KAAK,QAAQ;AAAA,QACnD,OAAO;AACL,iBAAO,SAAS,OAAO,OAAO,CAAC;AAAA,QACjC;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAWO,SAAS,oBAAoB,OAAO;AAIzC,QAAM,QAAQ,MAAM,QAAQ,GAAG;AAC/B,QAAM,eAAe,MAAM,QAAQ,GAAG;AACtC,QAAM,aAAa,MAAM,QAAQ,GAAG;AACpC,QAAM,QAAQ,MAAM,QAAQ,GAAG;AAE/B;AAAA;AAAA,IAEE,UAAU;AAAA,IAET,UAAU,MAAM,QAAQ,SACxB,iBAAiB,MAAM,QAAQ,gBAC/B,eAAe,MAAM,QAAQ;AAAA,IAE9B,aAAa,KAAK,MAAM,MAAM,GAAG,KAAK,CAAC;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;", "names": ["error", "tree"]}